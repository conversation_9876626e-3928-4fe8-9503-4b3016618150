import DictSelect from '@/components/DictSelect';
import { DatePicker, Form, Input, InputNumber, Modal } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import type { HistoricalElement } from '@/services/historicalElement';

const { TextArea } = Input;

export interface HistoricalElementFormProps {
  visible: boolean;
  loading: boolean;
  editingItem: HistoricalElement | null;
  onOk: (values: any) => void;
  onCancel: () => void;
}

export const HistoricalElementForm: React.FC<HistoricalElementFormProps> = ({
  visible,
  loading,
  editingItem,
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();

  // 当编辑项变化时，更新表单数据
  useEffect(() => {
    if (visible && editingItem) {
      const formData = {
        ...editingItem,
        constructionTime: editingItem.constructionTime ? dayjs(editingItem.constructionTime) : undefined,
      };
      form.setFieldsValue(formData);
    } else if (visible && !editingItem) {
      form.resetFields();
    }
  }, [visible, editingItem, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = {
        ...values,
        constructionTime: values.constructionTime 
          ? values.constructionTime.toISOString() 
          : undefined,
      };
      onOk(formattedValues);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={editingItem ? '编辑历史要素' : '添加历史要素'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={700}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入历史要素名称' }]}
        >
          <Input placeholder="请输入历史要素名称" />
        </Form.Item>

        <Form.Item
          name="code"
          label="编号"
          rules={[{ required: true, message: '请输入历史要素编号' }]}
        >
          <Input placeholder="请输入历史要素编号" />
        </Form.Item>

        <Form.Item
          name="typeDictId"
          label="类型"
          rules={[{ required: false, message: '请选择历史要素类型' }]}
        >
          <DictSelect.DictTreeSelect
            type="type"
            placeholder="请选择历史要素类型"
            allowClear
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="constructionLongitude"
          label="经度"
          rules={[
            { required: true, message: '请输入经度' },
            { type: 'number', min: -180, max: 180, message: '经度范围应在-180到180之间' }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入经度"
            precision={6}
            min={-180}
            max={180}
          />
        </Form.Item>

        <Form.Item
          name="constructionLatitude"
          label="纬度"
          rules={[
            { required: true, message: '请输入纬度' },
            { type: 'number', min: -90, max: 90, message: '纬度范围应在-90到90之间' }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入纬度"
            precision={6}
            min={-90}
            max={90}
          />
        </Form.Item>

        <Form.Item
          name="locationDescription"
          label="位置描述"
          rules={[{ required: false, message: '请输入位置描述' }]}
        >
          <Input placeholder="请输入位置描述" />
        </Form.Item>

        <Form.Item
          name="constructionTime"
          label="建造时间"
          rules={[{ required: false, message: '请选择建造时间' }]}
        >
          <DatePicker
            style={{ width: '100%' }}
            placeholder="请选择建造时间"
            showTime
          />
        </Form.Item>

        <Form.Item
          name="regionDictId"
          label="所属区域"
          rules={[{ required: true, message: '请选择所属区域' }]}
        >
          <DictSelect.DictTreeSelect
            type="region"
            placeholder="请选择所属区域"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="historicalRecords"
          label="历史记载"
          rules={[{ required: false, message: '请输入历史记载' }]}
        >
          <TextArea rows={4} placeholder="请输入历史记载" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
