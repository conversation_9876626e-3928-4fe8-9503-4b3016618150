import DictSelect from '@/components/DictSelect';
import {
  batchImportHistoricalElements,
  createHistoricalElement,
  deleteHistoricalElement,
  getHistoricalElementList,
  getHistoricalElementStatistics,
  getHistoricalElementTimeline,
  updateHistoricalElement,
  type HistoricalElement,
  type CreateHistoricalElementParams,
  type UpdateHistoricalElementParams,
  type HistoricalElementStatistics,
  type TimelineData,
} from '@/services/historicalElement';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Statistic,
  Table,
  Tabs,
  Timeline,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';

const { Title } = Typography;
const { TextArea } = Input;

const AdminHistoricalElement: React.FC = () => {
  // 状态管理
  const [data, setData] = useState<HistoricalElement[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<HistoricalElement | null>(null);
  const [form] = Form.useForm();

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [regionFilter, setRegionFilter] = useState<number | undefined>();
  const [typeFilter, setTypeFilter] = useState<number | undefined>();

  // 批量操作状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importForm] = Form.useForm();

  // 统计和时间轴状态
  const [statistics, setStatistics] = useState<HistoricalElementStatistics | null>(null);
  const [timelineData, setTimelineData] = useState<TimelineData[]>([]);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [timelineLoading, setTimelineLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  // 获取历史要素列表
  const fetchData = useCallback(async (
    page = 1,
    pageSize = 10,
    keyword = searchKeyword,
    regionId = regionFilter,
    typeId = typeFilter,
  ) => {
    setLoading(true);
    try {
      const response = await getHistoricalElementList({
        page,
        pageSize,
        keyword: keyword || undefined,
        regionId,
        typeId,
      });

      if (response.errCode === 0 && response.data) {
        setData(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.msg || '获取历史要素列表失败');
      }
    } catch (error: any) {
      console.error('获取历史要素列表失败:', error);
      message.error(error?.response?.data?.msg || '获取历史要素列表失败');
    } finally {
      setLoading(false);
    }
  }, [searchKeyword, regionFilter, typeFilter]);

  // 获取统计数据
  const fetchStatistics = useCallback(async () => {
    setStatisticsLoading(true);
    try {
      const response = await getHistoricalElementStatistics({
        regionId: regionFilter,
        typeId: typeFilter,
      });

      if (response.errCode === 0 && response.data) {
        setStatistics(response.data);
      } else {
        message.error(response.msg || '获取统计数据失败');
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
      message.error(error?.response?.data?.msg || '获取统计数据失败');
    } finally {
      setStatisticsLoading(false);
    }
  }, [regionFilter, typeFilter]);

  // 获取时间轴数据
  const fetchTimelineData = useCallback(async () => {
    setTimelineLoading(true);
    try {
      const response = await getHistoricalElementTimeline({
        regionId: regionFilter,
      });

      if (response.errCode === 0 && response.data) {
        setTimelineData(response.data);
      } else {
        message.error(response.msg || '获取时间轴数据失败');
      }
    } catch (error: any) {
      console.error('获取时间轴数据失败:', error);
      message.error(error?.response?.data?.msg || '获取时间轴数据失败');
    } finally {
      setTimelineLoading(false);
    }
  }, [regionFilter]);

  // 初始化数据
  useEffect(() => {
    fetchData();
    if (activeTab === 'statistics') {
      fetchStatistics();
    } else if (activeTab === 'timeline') {
      fetchTimelineData();
    }
  }, [fetchData, fetchStatistics, fetchTimelineData, activeTab]);

  // 处理函数
  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: HistoricalElement) => {
    setEditingItem(record);
    const formData = {
      ...record,
      constructionTime: record.constructionTime ? dayjs(record.constructionTime) : undefined,
    };
    form.setFieldsValue(formData);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteHistoricalElement(id);
      if (response.errCode === 0) {
        message.success('删除成功！');
        fetchData(pagination.current, pagination.pageSize);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error: any) {
      console.error('删除失败:', error);
      message.error(error?.response?.data?.msg || '删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues: CreateHistoricalElementParams | UpdateHistoricalElementParams = {
        ...values,
        constructionTime: values.constructionTime
          ? values.constructionTime.toISOString()
          : undefined,
      };

      let response;
      if (editingItem) {
        response = await updateHistoricalElement(editingItem.id, formattedValues);
        if (response.errCode === 0) {
          message.success('编辑成功！');
        }
      } else {
        response = await createHistoricalElement(formattedValues as CreateHistoricalElementParams);
        if (response.errCode === 0) {
          message.success('添加成功！');
        }
      }

      if (response.errCode === 0) {
        setModalVisible(false);
        fetchData(pagination.current, pagination.pageSize);
      } else {
        message.error(response.msg || '操作失败');
      }
    } catch (error: any) {
      console.error('操作失败:', error);
      message.error(error?.response?.data?.msg || '操作失败');
    }
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    fetchData(1, pagination.pageSize, value, regionFilter, typeFilter);
  };

  const handleRegionFilterChange = (value: number | undefined) => {
    setRegionFilter(value);
    fetchData(1, pagination.pageSize, searchKeyword, value, typeFilter);
  };

  const handleTypeFilterChange = (value: number | undefined) => {
    setTypeFilter(value);
    fetchData(1, pagination.pageSize, searchKeyword, regionFilter, value);
  };

  const handleRefresh = () => {
    fetchData(pagination.current, pagination.pageSize);
  };

  const handleReset = () => {
    setSearchKeyword('');
    setRegionFilter(undefined);
    setTypeFilter(undefined);
    setSelectedRowKeys([]);
    fetchData(1, pagination.pageSize, '', undefined, undefined);
  };

  // 批量操作处理
  const handleSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const clearSelection = () => {
    setSelectedRowKeys([]);
  };

  const checkSelection = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的记录');
      return false;
    }
    return true;
  };

  const handleBatchDelete = async () => {
    if (!checkSelection()) return;

    Modal.confirm({
      title: '批量删除确认',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        setBatchLoading(true);
        try {
          const deletePromises = selectedRowKeys.map(id =>
            deleteHistoricalElement(Number(id))
          );

          const results = await Promise.allSettled(deletePromises);
          const successCount = results.filter(result =>
            result.status === 'fulfilled' && result.value.errCode === 0
          ).length;

          if (successCount === selectedRowKeys.length) {
            message.success(`成功删除 ${successCount} 条记录`);
          } else {
            message.warning(`删除了 ${successCount}/${selectedRowKeys.length} 条记录`);
          }

          clearSelection();
          fetchData(pagination.current, pagination.pageSize);
        } catch (error: any) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        } finally {
          setBatchLoading(false);
        }
      },
    });
  };

  const handleImportModalOpen = () => {
    setImportModalVisible(true);
    importForm.resetFields();
  };

  const handleImportModalClose = () => {
    setImportModalVisible(false);
    importForm.resetFields();
  };

  const handleBatchImport = async () => {
    try {
      const values = await importForm.validateFields();
      const { importData } = values;

      if (!importData || importData.trim() === '') {
        message.error('请输入导入数据');
        return;
      }

      setBatchLoading(true);

      // 解析JSON数据
      let elements;
      try {
        elements = JSON.parse(importData);
        if (!Array.isArray(elements)) {
          throw new Error('数据格式错误，应为数组格式');
        }
      } catch (parseError) {
        message.error('JSON格式错误，请检查数据格式');
        return;
      }

      const response = await batchImportHistoricalElements({ elements });

      if (response.errCode === 0) {
        message.success('批量导入成功');
        handleImportModalClose();
        fetchData(1, pagination.pageSize);
      } else {
        message.error(response.msg || '批量导入失败');
      }
    } catch (error: any) {
      console.error('批量导入失败:', error);
      message.error(error?.response?.data?.msg || '批量导入失败');
    } finally {
      setBatchLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '类型',
      dataIndex: 'typeDictId',
      key: 'typeDictId',
      render: (_: any, record: HistoricalElement) => {
        return record.typeDict?.typeName || '未知类型';
      },
    },
    {
      title: '建造时间',
      dataIndex: 'constructionTime',
      key: 'constructionTime',
      render: (time: string) => time ? dayjs(time).format('YYYY年MM月DD日') : '-',
    },
    {
      title: '所属区域',
      dataIndex: 'regionDictId',
      key: 'regionDictId',
      render: (_: any, record: HistoricalElement) => {
        return record.regionDict?.regionName || '未知区域';
      },
    },
    {
      title: '位置',
      key: 'location',
      render: (_: any, record: HistoricalElement) => (
        <span>
          {record.constructionLongitude.toFixed(4)}, {record.constructionLatitude.toFixed(4)}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: HistoricalElement) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 渲染统计卡片
  const renderStatisticsCards = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总数量" value={statistics.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="类型数量" value={statistics.byType.length} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="区域数量" value={statistics.byRegion.length} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="时期数量" value={statistics.byPeriod.length} />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染统计详情
  const renderStatisticsDetails = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16}>
        <Col span={8}>
          <Card title="按类型统计" loading={statisticsLoading}>
            {statistics.byType.map(item => (
              <div key={item.typeId} style={{ marginBottom: 8 }}>
                <span>{item.typeName}: </span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="按区域统计" loading={statisticsLoading}>
            {statistics.byRegion.map(item => (
              <div key={item.regionId} style={{ marginBottom: 8 }}>
                <span>{item.regionName}: </span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="按时期统计" loading={statisticsLoading}>
            {statistics.byPeriod.map(item => (
              <div key={item.period} style={{ marginBottom: 8 }}>
                <span>{item.period}: </span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染时间轴
  const renderTimeline = () => {
    return (
      <Card title="历史要素时间轴" loading={timelineLoading}>
        <Timeline mode="left">
          {timelineData.map(item => (
            <Timeline.Item key={item.year} label={`${item.year}年`}>
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
                  共 {item.count} 个历史要素
                </div>
                {item.elements.map(element => (
                  <div key={element.id} style={{ marginBottom: 4 }}>
                    • {element.name}
                  </div>
                ))}
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <Title level={2}>历史要素管理</Title>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加历史要素
          </Button>
          <Button onClick={handleImportModalOpen}>
            批量导入
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'list',
            label: '数据列表',
            children: (
              <div>
                {/* 搜索和筛选区域 */}
                <Card style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={6}>
                      <Input.Search
                        placeholder="搜索历史要素名称"
                        allowClear
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        onSearch={handleSearch}
                        enterButton={<SearchOutlined />}
                      />
                    </Col>
                    <Col span={4}>
                      <DictSelect.DictTreeSelect
                        type="region"
                        placeholder="选择区域"
                        allowClear
                        value={regionFilter}
                        onChange={handleRegionFilterChange}
                        style={{ width: '100%' }}
                      />
                    </Col>
                    <Col span={4}>
                      <DictSelect.DictTreeSelect
                        type="type"
                        placeholder="选择类型"
                        allowClear
                        value={typeFilter}
                        onChange={handleTypeFilterChange}
                        style={{ width: '100%' }}
                      />
                    </Col>
                    <Col span={6}>
                      <Space>
                        <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                          刷新
                        </Button>
                        <Button onClick={handleReset}>重置</Button>
                      </Space>
                    </Col>
                  </Row>
                </Card>

                {/* 批量操作栏 */}
                {selectedRowKeys.length > 0 && (
                  <Card style={{ marginBottom: 16, backgroundColor: '#f0f2f5' }}>
                    <Row justify="space-between" align="middle">
                      <Col>
                        <Space>
                          <span>已选择 {selectedRowKeys.length} 项</span>
                          <Button size="small" onClick={clearSelection}>
                            取消选择
                          </Button>
                        </Space>
                      </Col>
                      <Col>
                        <Space>
                          <Button
                            size="small"
                            danger
                            loading={batchLoading}
                            onClick={handleBatchDelete}
                          >
                            批量删除
                          </Button>
                        </Space>
                      </Col>
                    </Row>
                  </Card>
                )}

                <Table
                  columns={columns}
                  dataSource={data}
                  rowKey="id"
                  loading={loading}
                  rowSelection={{
                    selectedRowKeys,
                    onChange: handleSelectChange,
                  }}
                  pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range?.[0]}-${range?.[1]} 条，共 ${total} 条记录`,
                    onChange: (page, pageSize) => {
                      fetchData(page, pageSize);
                    },
                    onShowSizeChange: (_, size) => {
                      fetchData(1, size);
                    },
                  }}
                />
              </div>
            ),
          },
          {
            key: 'statistics',
            label: '统计分析',
            children: (
              <div>
                {renderStatisticsCards()}
                {renderStatisticsDetails()}
              </div>
            ),
          },
          {
            key: 'timeline',
            label: '时间轴',
            children: renderTimeline(),
          },
        ]}
      />

      <Modal
        title={editingItem ? '编辑历史要素' : '添加历史要素'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={700}
        destroyOnHidden
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入历史要素名称' }]}
          >
            <Input placeholder="请输入历史要素名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="编号"
            rules={[{ required: true, message: '请输入历史要素编号' }]}
          >
            <Input placeholder="请输入历史要素编号" />
          </Form.Item>

          <Form.Item
            name="typeDictId"
            label="类型"
            rules={[{ required: false, message: '请选择历史要素类型' }]}
          >
            <DictSelect.DictTreeSelect
              type="type"
              placeholder="请选择历史要素类型"
              allowClear
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="constructionLongitude"
            label="经度"
            rules={[
              { required: true, message: '请输入经度' },
              { type: 'number', min: -180, max: 180, message: '经度范围应在-180到180之间' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入经度"
              precision={6}
              min={-180}
              max={180}
            />
          </Form.Item>

          <Form.Item
            name="constructionLatitude"
            label="纬度"
            rules={[
              { required: true, message: '请输入纬度' },
              { type: 'number', min: -90, max: 90, message: '纬度范围应在-90到90之间' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入纬度"
              precision={6}
              min={-90}
              max={90}
            />
          </Form.Item>

          <Form.Item
            name="locationDescription"
            label="位置描述"
            rules={[{ required: false, message: '请输入位置描述' }]}
          >
            <Input placeholder="请输入位置描述" />
          </Form.Item>

          <Form.Item
            name="constructionTime"
            label="建造时间"
            rules={[{ required: false, message: '请选择建造时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="请选择建造时间"
              showTime
            />
          </Form.Item>

          <Form.Item
            name="regionDictId"
            label="所属区域"
            rules={[{ required: true, message: '请选择所属区域' }]}
          >
            <DictSelect.DictTreeSelect
              type="region"
              placeholder="请选择所属区域"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="historicalRecords"
            label="历史记载"
            rules={[{ required: false, message: '请输入历史记载' }]}
          >
            <TextArea rows={4} placeholder="请输入历史记载" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量导入模态框 */}
      <Modal
        title="批量导入历史要素"
        open={importModalVisible}
        onOk={handleBatchImport}
        onCancel={handleImportModalClose}
        width={800}
        confirmLoading={batchLoading}
        destroyOnHidden
      >
        <Form form={importForm} layout="vertical">
          <Form.Item
            name="importData"
            label="导入数据"
            rules={[{ required: true, message: '请输入导入数据' }]}
            extra={
              <div>
                <p>请输入JSON格式的历史要素数据数组，示例格式：</p>
                <pre style={{
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  overflow: 'auto'
                }}>
{`[
  {
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名慈恩寺塔...",
    "regionDictId": 1
  }
]`}
                </pre>
              </div>
            }
          >
            <TextArea
              rows={10}
              placeholder="请输入JSON格式的历史要素数据..."
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminHistoricalElement;
