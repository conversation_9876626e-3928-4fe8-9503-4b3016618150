/* 历史要素表单样式 */

.formContainer {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}

.formContainer::-webkit-scrollbar {
  width: 6px;
}

.formContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.formContainer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.formContainer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.sectionCard {
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.sectionCard:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.sectionTitle {
  font-weight: 600;
  color: #262626;
  margin-bottom: 0;
}

.fieldLabel {
  font-weight: 500;
  color: #595959;
}

.tooltipIcon {
  color: #1890ff;
  cursor: help;
}

.coordinateInput {
  font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
}

.textArea {
  resize: vertical;
  min-height: 120px;
}

.modalTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formContainer {
    max-height: 60vh;
  }
  
  .sectionCard {
    margin-bottom: 12px;
  }
}
